---
import { getLangFromUrl, useTranslations } from '../i18n/utils';

export interface Props {
  lat: number;
  lon: number;
  title: string;
  address?: string;
  className?: string;
  rating?: number;
}

const { lat, lon, title, address, className = "w-full h-64 lg:h-80 rounded-xl shadow-lg", rating } = Astro.props;

// Get current language and translation function
const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);

// Generate Google Maps URLs
const mapsUrl = `https://maps.google.com/?q=${lat},${lon}`;

// Google Maps embed URL (same for both themes, we'll use CSS filters for dark mode)
const mapsEmbedUrl = `https://maps.google.com/maps?q=${lat},${lon}&t=&z=15&ie=UTF8&iwloc=&output=embed`;
---

<div
  class={`relative ${className}`}
  x-data={`{
    showMap: false,
    isDarkMode: false,
    mapUrl: '${mapsEmbedUrl}'
  }`}
  x-init="
    isDarkMode = document.documentElement.classList.contains('dark');
    // Watch for theme changes
    const observer = new MutationObserver(() => {
      isDarkMode = document.documentElement.classList.contains('dark');
    });
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });
  "
>
  <!-- Map placeholder with click to load -->
  <div
    x-show="!showMap"
    class="w-full h-full bg-gradient-to-br from-blue-100 to-green-100 dark:from-blue-900 dark:to-green-900 rounded-xl flex flex-col items-center justify-center cursor-pointer hover:from-blue-200 hover:to-green-200 dark:hover:from-blue-800 dark:hover:to-green-800 transition-all duration-300"
    @click="showMap = true"
  >
    <div class="text-center p-6">
      <div class="text-4xl mb-4">📍</div>
      <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">
        {title}
      </h3>
      {address && (
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
          {address}
        </p>
      )}
      <div class="bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors inline-block">
        {t('googleMaps.clickToViewMap')}
      </div>
    </div>
  </div>

  <!-- Google Maps Embed -->
  <div x-show="showMap" x-transition class="w-full h-full" x-data="{ mapLoaded: false, mapError: false }">
    <iframe
      :src="mapUrl"
      width="100%"
      height="100%"
      style="border:0;"
      :style="isDarkMode ? 'border:0; filter: invert(0.9) hue-rotate(180deg) brightness(0.9) contrast(1.1);' : 'border:0;'"
      allowfullscreen=""
      loading="lazy"
      referrerpolicy="no-referrer-when-downgrade"
      title={`Map showing location of ${title}`}
      class="rounded-xl"
      @load="mapLoaded = true"
      @error="mapError = true"
    ></iframe>

    <!-- Loading indicator -->
    <div x-show="!mapLoaded && !mapError" class="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-xl">
      <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2"></div>
        <p class="text-sm text-gray-600 dark:text-gray-400">{t('googleMaps.loadingMap')}</p>
      </div>
    </div>

    <!-- Error fallback -->
    <div x-show="mapError" class="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-xl">
      <div class="text-center p-6">
        <div class="text-2xl mb-2">🗺️</div>
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">{t('googleMaps.mapLoadError')}</p>
        <a
          href={mapsUrl}
          target="_blank"
          rel="noopener noreferrer"
          class="bg-primary-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-primary-700 transition-colors"
        >
          {t('googleMaps.openInGoogleMaps')}
        </a>
      </div>
    </div>
  </div>

  <!-- Map controls overlay -->
  <div class="absolute top-4 right-4 flex flex-row gap-2 items-center">
    {/* {rating && (
      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent-100 text-accent-800 dark:bg-accent-900 dark:text-accent-200 shadow-lg">
        ⭐ {rating}
      </span>
    )} */}
    <a
      href={mapsUrl}
      target="_blank"
      rel="noopener noreferrer"
      class="bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 px-3 py-2 rounded-lg text-sm font-medium hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors shadow-lg"
      title={t('googleMaps.openInGoogleMaps')}
    >
      🗺️ {t('googleMaps.googleMaps')}
    </a>
  </div>
</div>

<script>
  // Future enhancement: Add Google Maps JavaScript API integration
  // This would require an API key and more advanced setup

  // Example for future implementation:
  // function initGoogleMap(lat, lon, title) {
  //   const map = new google.maps.Map(document.getElementById('map'), {
  //     center: { lat: lat, lng: lon },
  //     zoom: 15
  //   });
  //
  //   new google.maps.Marker({
  //     position: { lat: lat, lng: lon },
  //     map: map,
  //     title: title
  //   });
  // }
</script>
